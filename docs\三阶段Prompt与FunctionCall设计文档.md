# 🧠 APIICL 三阶段Prompt与Function Call设计文档

## 📋 系统概述

APIICL是一个基于元认知智能体的命名实体识别(NER)系统，采用三阶段统一处理架构：

1. **阶段1**: 统一生成检索请求 (使用Function Call)
2. **阶段2**: 统一检索 (基于请求检索示例)  
3. **阶段3**: 统一NER (基于示例进行实体识别)

## 🔧 核心架构

### 系统流程图
```
输入文本 → 阶段1(分析+Function Call) → 阶段2(检索示例) → 阶段3(NER提取) → 输出实体
```

### 关键组件
- **MetaCognitiveAgent**: 元认知智能体核心引擎
- **ModelService**: 统一模型服务层，支持Function Calling
- **ExampleRetriever**: 示例检索器
- **Schemas**: Pydantic数据模型定义

---

## 🎯 阶段1: 统一生成检索请求

### 目标
让LLM分析输入文本，理解NER任务的难点，并通过Function Call生成精确的检索请求。

### Prompt设计

```python
def _build_stage1_prompt(self, text: str) -> str:
    entity_types = self._get_current_entity_types()
    entity_types_str = ', '.join(entity_types)

    return f"""Task: Retrieve NER examples for entity extraction.

Entity types: {entity_types_str}
Input text: "{text}"

Call retrieve_ner_examples tool with:
- description: Brief description of needed examples (max 50 words)
- k: Number of examples (1-3)

Requirements:
- Keep description concise and specific
- Focus only on the most relevant example type needed
- Do not explain reasoning or provide detailed analysis

MUST call retrieve_ner_examples tool now."""
```

### Prompt设计原则

1. **简洁明确**: 避免冗长的说明，直接指明任务目标
2. **强制调用**: 使用"MUST call"确保LLM执行Function Call
3. **参数约束**: 明确限制description长度和k值范围
4. **上下文感知**: 提供实体类型信息帮助LLM理解任务

### Function Call工具定义

```python
class RetrieveNERExamplesTool(BaseModel):
    """🧠 检索NER示例工具 - LLM通过此工具获取相关教学示例"""
    description: str = Field(
        description="详细描述需要什么样的NER示例来帮助解决当前文本的实体识别难点。请具体说明遇到的问题和期望的示例类型。"
    )
    k: int = Field(
        description="需要检索的示例数量，建议1-5个",
        default=3,
        ge=1,
        le=10
    )
```

### 执行流程

1. **构建Prompt**: 包含实体类型和输入文本
2. **调用LLM**: 使用`generate_with_tools_async`方法
3. **解析Function Call**: 提取`description`和`k`参数
4. **容错处理**: 处理JSON解析错误，提供降级方案

---

## 🔍 阶段2: 统一检索

### 目标
基于阶段1生成的检索请求，从向量数据库中检索最相关的NER示例。

### 检索机制

```python
async def _execute_retrieval_stage(self, tool_calls: List[Any]) -> List[Any]:
    for tool_call in tool_calls:
        if tool_call.function.name == "RetrieveNERExamplesTool":
            try:
                arguments = json.loads(tool_call.function.arguments)
                description = arguments.get("description", "")
                k = arguments.get("k", 3)
                
                # 执行检索
                examples = await self._simple_retrieval(description, k)
                return examples
            except json.JSONDecodeError:
                # 容错机制：修复JSON或使用默认参数
                examples = await self._simple_retrieval("general NER examples", 3)
                return examples
```

### 容错策略

1. **JSON修复**: 自动修复常见的转义字符问题
2. **降级检索**: 解析失败时使用默认参数
3. **日志记录**: 详细记录检索过程和结果

---

## 🎯 阶段3: 统一NER

### 目标
基于检索到的few-shot示例，构建上下文丰富的prompt进行命名实体识别。

### Prompt设计

```python
def _execute_ner_stage(self, text: str, few_shot_examples: List[Any]) -> Dict[str, List[str]]:
    examples_text = self._format_examples_for_context(few_shot_examples)
    entity_types = self._get_current_entity_types()
    entity_types_str = ', '.join(entity_types)

    ner_prompt = f"""Extract named entities from text.

Examples:
{examples_text}

Entity types: {entity_types_str}
Text: "{text}"

Output only JSON format: {{"person": ["name1"], "organization": ["org1"], "location": ["place1"]}}
If no entities found, return: {{}}"""
```

### 示例格式化

```python
def _format_examples_for_context(self, examples) -> str:
    formatted_examples = []
    for i, example in enumerate(examples, 1):
        # 处理ScoredExample对象或普通字典
        example_data = example.example if hasattr(example, 'example') else example
        
        text = example_data.get('text', '')
        labels = example_data.get('label', {})
        
        entities_str = ", ".join(
            f"'{entity}' ({etype})"
            for etype, entities in labels.items()
            for entity in entities
        )
        
        formatted_examples.append(f"Example {i}:\nText: {text}\nEntities: [{entities_str}]")
    
    return "\n\n".join(formatted_examples)
```

### 输出解析

```python
# 使用正则表达式提取JSON
json_match = re.search(r'\{.*\}', response, re.DOTALL)
if json_match:
    entities_json = json_match.group()
    entities = json.loads(entities_json)
    return entities
```

---

## 🛠️ Function Calling技术实现

### 模型服务层

```python
class ModelService:
    async def generate_with_tools_async(self, messages: List[Dict], tools: List[Type[BaseModel]]):
        tool_schemas = [pydantic_to_openai_tool(tool) for tool in tools]
        
        response = await client.chat.completions.create(
            model=self.model_name,
            messages=messages,
            tools=tool_schemas,
            tool_choice="auto",
        )
        return response.choices[0].message
```

### Pydantic到OpenAI Schema转换

```python
def pydantic_to_openai_tool(pydantic_model: Type[BaseModel]) -> Dict[str, Any]:
    schema = pydantic_model.model_json_schema()
    return {
        "type": "function",
        "function": {
            "name": schema.get('title', pydantic_model.__name__),
            "description": schema.get('description', ''),
            "parameters": schema
        }
    }
```

### 工具调用解析

```python
if response and hasattr(response, 'tool_calls') and response.tool_calls:
    for tool_call in response.tool_calls:
        function_name = tool_call.function.name
        arguments = json.loads(tool_call.function.arguments)
        # 执行相应的工具逻辑
```

---

## 📊 性能优化策略

### 并发处理
- **批量发送**: 将请求分批处理，避免同时发送过多请求
- **异步执行**: 使用`asyncio`实现高并发处理
- **信号量控制**: 限制最大并发数，防止API限流

### 缓存机制
- **请求缓存**: 缓存阶段1的检索请求结果
- **示例缓存**: 缓存阶段2的检索示例
- **结果缓存**: 缓存阶段3的NER结果

### 内存优化
- **及时清理**: 处理完成后立即清理大型列表
- **垃圾回收**: 强制执行垃圾回收释放内存
- **连接复用**: 复用HTTP连接池减少开销

---

## 🔧 配置与扩展

### 数据集配置
```python
# 支持多种NER数据集
DATASETS = {
    'ace2005': {
        'name': 'ACE 2005',
        'labels': ['person', 'organization', 'location', 'facility', 'gpe'],
        'path': './data/ACE 2005/train.json'
    },
    'conll2003': {
        'name': 'CoNLL2003', 
        'labels': ['person', 'organization', 'location', 'miscellaneous'],
        'path': './data/CoNLL2003/train.json'
    }
}
```

### 批处理配置
```python
CONFIG = {
    'batch_size': 200,        # 批次大小
    'batch_delay': 1.0,       # 批次间隔
    'max_concurrent_requests': 500,  # 最大并发数
    'api_timeout': 60,        # API超时时间
    'max_retries': 3,         # 最大重试次数
}
```

---

## 📈 监控与调试

### 进度管理
- **实时进度条**: 显示每个阶段的处理进度
- **成功率统计**: 记录成功和失败的任务数量
- **耗时统计**: 计算每个阶段的处理时间

### 日志系统
- **分级日志**: 支持DEBUG/INFO/WARNING/ERROR级别
- **详细追踪**: 记录每个步骤的执行情况
- **错误处理**: 详细记录异常信息和堆栈

### 结果评估
```python
eval_results = {
    'dataset': current_dataset['name'],
    'timestamp': datetime.now().isoformat(),
    'samples_count': len(test_data),
    'precision': precision,
    'recall': recall,
    'f1_score': f1_score,
    'processing_mode': 'unified_three_stage'
}
```

---

## 🎯 最佳实践

### Prompt设计原则
1. **明确指令**: 使用清晰、具体的指令
2. **格式约束**: 明确输出格式要求
3. **示例引导**: 提供高质量的few-shot示例
4. **容错设计**: 考虑各种边界情况

### Function Call设计
1. **参数验证**: 使用Pydantic进行严格的参数验证
2. **错误处理**: 实现完善的错误处理和降级机制
3. **工具组合**: 合理设计工具的组合和调用顺序
4. **性能优化**: 考虑工具调用的性能影响

### 系统架构
1. **模块化设计**: 清晰的模块划分和接口定义
2. **异步处理**: 充分利用异步编程提升性能
3. **资源管理**: 合理管理内存和网络资源
4. **可扩展性**: 设计支持新数据集和新功能的扩展机制

---

## 📚 总结

本系统通过三阶段的设计，将复杂的NER任务分解为：
1. **智能分析**: LLM理解任务并生成检索需求
2. **精准检索**: 基于需求检索最相关的示例
3. **上下文推理**: 利用示例进行高质量的实体识别

Function Calling机制确保了LLM能够准确调用外部工具，而精心设计的Prompt保证了每个阶段的输出质量。整个系统在保持高性能的同时，提供了良好的可扩展性和可维护性。
