#!/usr/bin/env python3
"""
🔍 FAISS检索问题调试脚本
用于检查FAISS索引状态和向量数据库内容
"""

import asyncio
import logging
from example_retriever import example_retriever
from config import CONFIG

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def debug_faiss_status():
    """调试FAISS状态和检索问题"""
    
    print("🔍 开始调试FAISS检索问题...")
    print("=" * 60)
    
    # 1. 检查检索器初始化状态
    print(f"📊 检索器初始化状态: {example_retriever.initialized}")
    
    if not example_retriever.initialized:
        print("🔧 正在初始化检索器...")
        await example_retriever.initialize_vector_store()
        print(f"📊 初始化后状态: {example_retriever.initialized}")
    
    # 2. 检查向量存储状态
    vector_store = example_retriever.vector_store
    print(f"📊 向量存储类型: {type(vector_store).__name__}")
    print(f"📊 向量存储初始化状态: {vector_store.initialized}")
    
    # 3. 检查数据量
    print(f"📊 示例总数: {len(vector_store.examples)}")
    print(f"📊 嵌入向量总数: {len(vector_store.embeddings)}")
    print(f"📊 元数据总数: {len(vector_store.metadata)}")
    
    if vector_store.dimension:
        print(f"📊 向量维度: {vector_store.dimension}")
    
    # 4. 检查FAISS索引状态
    if hasattr(vector_store, 'index') and vector_store.index:
        print(f"📊 FAISS索引类型: {type(vector_store.index).__name__}")
        print(f"📊 FAISS索引中的向量数: {vector_store.index.ntotal}")
        
        # 检查索引是否已训练（对于需要训练的索引）
        if hasattr(vector_store.index, 'is_trained'):
            print(f"📊 FAISS索引训练状态: {vector_store.index.is_trained}")
    else:
        print("❌ FAISS索引未创建")
    
    # 5. 显示前几个示例
    print("\n📝 前5个示例预览:")
    for i, example in enumerate(vector_store.examples[:5]):
        text_preview = example.get('text', '')[:50] + "..." if len(example.get('text', '')) > 50 else example.get('text', '')
        print(f"  {i+1}. {text_preview}")
        print(f"     标签: {example.get('label', {})}")
    
    # 6. 测试检索功能
    print("\n🧪 测试检索功能:")
    test_descriptions = [
        "person name entity",
        "organization company",
        "location place"
    ]
    
    for desc in test_descriptions:
        print(f"\n🔍 测试检索: '{desc}'")
        
        # 测试不同的k值
        for k in [1, 2, 3, 5]:
            try:
                results = await example_retriever.simple_retrieve(desc, k)
                print(f"  k={k}: 返回 {len(results)} 个结果")
                
                # 显示结果详情
                for i, result in enumerate(results):
                    text_preview = result.get('text', '')[:30] + "..." if len(result.get('text', '')) > 30 else result.get('text', '')
                    score = result.get('similarity_score', 0.0)
                    print(f"    {i+1}. {text_preview} (score: {score:.4f})")
                    
            except Exception as e:
                print(f"  k={k}: 检索失败 - {e}")
    
    # 7. 直接测试FAISS搜索
    print("\n🔬 直接测试FAISS搜索:")
    if vector_store.initialized and vector_store.embeddings:
        try:
            # 使用第一个向量作为查询向量进行测试
            test_embedding = vector_store.embeddings[0]
            
            for k in [1, 2, 3, 5]:
                search_results = await vector_store.search(test_embedding, top_k=k)
                print(f"  直接FAISS搜索 k={k}: 返回 {len(search_results)} 个结果")
                
                for i, (idx, score) in enumerate(search_results):
                    print(f"    {i+1}. 索引{idx}, 分数{score:.4f}")
                    
        except Exception as e:
            print(f"  直接FAISS搜索失败: {e}")
    
    print("\n" + "=" * 60)
    print("🔍 调试完成")

if __name__ == "__main__":
    asyncio.run(debug_faiss_status())
