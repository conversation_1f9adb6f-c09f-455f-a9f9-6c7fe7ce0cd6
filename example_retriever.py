"""
示例检索器 - 简单高效的NER示例检索系统
遵循KISS原则：保持简单，避免过度设计
"""

import asyncio
import logging
import numpy as np
from typing import List, Dict, Any, Optional, Tuple
import json
import os
import time
import pickle
import hashlib
import threading
from collections import defaultdict

try:
    import faiss
    FAISS_AVAILABLE = True
    # {{ AURA-X: Add - 启用FAISS多线程优化，提升检索性能. Approval: 寸止(ID:1738230400). }}
    # 设置FAISS多线程数量为CPU核心数的一半，避免过度竞争
    import os
    cpu_count = os.cpu_count() or 4
    faiss_threads = max(1, cpu_count // 2)
    try:
        faiss.omp_set_num_threads(faiss_threads)
        logging.info(f"✅ FAISS多线程已启用: {faiss_threads} 线程")
    except AttributeError:
        # 某些FAISS版本可能没有omp_set_num_threads
        logging.info("✅ FAISS已加载，使用默认线程配置")
except ImportError:
    FAISS_AVAILABLE = False
    logging.warning("FAISS not available, falling back to simple vector search")

from schemas import RetrievalCriteria
from model_interface import model_service
from config import CONFIG, get_current_dataset_path

logger = logging.getLogger(__name__)


class BatchProcessor:
    """异步批处理器 - 收集并发请求进行批量处理"""

    def __init__(self, batch_size: int = 32, timeout: float = 0.5):
        # {{ AURA-X: Fix - 统一并优化批处理参数，平衡性能和响应速度. Approval: 寸止(ID:1738232000). }}
        self.batch_size = batch_size  # 32个请求，平衡批处理效果和响应速度
        self.timeout = timeout  # 0.5秒，在高并发下有足够聚合时间
        self.pending_requests = []
        self.request_futures = {}
        self.lock = asyncio.Lock()
        self.processing = False

    async def add_request(self, request_id: str, description: str) -> List[float]:
        """添加嵌入请求到批处理队列"""
        future = asyncio.Future()

        async with self.lock:
            self.pending_requests.append((request_id, description))
            self.request_futures[request_id] = future

            # {{ AURA-X: Fix - 修复任务引用管理，避免重复创建任务. Approval: 寸止(ID:1738232200). }}
            # 如果达到批处理大小，立即触发处理
            if len(self.pending_requests) >= self.batch_size:
                if not self.processing and not hasattr(self, '_batch_task'):
                    self._batch_task = asyncio.create_task(self._process_batch())
            # 如果没有正在处理的批次，启动新的批次处理
            elif not self.processing and not hasattr(self, '_batch_task'):
                self._batch_task = asyncio.create_task(self._process_batch())

        return await future

    async def _process_batch(self):
        """处理当前批次的请求 - 修复竞态条件"""
        # {{ AURA-X: Fix - 修复严重的竞态条件，确保并发安全. Approval: 寸止(ID:1738232000). }}
        async with self.lock:
            if self.processing:
                return
            self.processing = True

        try:
            # {{ AURA-X: Fix - 循环处理，确保所有请求都被处理. Approval: 寸止(ID:1738232100). }}
            while True:
                # 等待一小段时间收集更多请求
                await asyncio.sleep(self.timeout)

                current_batch = []
                request_ids = []

                async with self.lock:
                    if not self.pending_requests:
                        break  # 没有待处理请求，退出循环

                    # 获取当前批次
                    current_batch = self.pending_requests.copy()
                    self.pending_requests.clear()

                # 批量生成嵌入
                descriptions = [desc for _, desc in current_batch]
                request_ids = [req_id for req_id, _ in current_batch]

                logger.info(f"🔄 处理批次: {len(descriptions)} 个嵌入请求")

                try:
                    embeddings = await model_service.get_embeddings_async(descriptions)

                    # 分发结果 - 增强异常安全性
                    async with self.lock:
                        for i, request_id in enumerate(request_ids):
                            if request_id in self.request_futures:
                                future = self.request_futures.pop(request_id)
                                try:
                                    if i < len(embeddings):
                                        future.set_result(embeddings[i])
                                    else:
                                        future.set_result([])
                                except Exception as e:
                                    logger.warning(f"设置Future结果失败: {e}")

                    logger.info(f"✅ 批次处理完成: {len(embeddings)} 个嵌入")

                except Exception as batch_error:
                    # 处理失败，返回空结果 - 增强异常安全性
                    async with self.lock:
                        for request_id in request_ids:
                            if request_id in self.request_futures:
                                future = self.request_futures.pop(request_id)
                                try:
                                    future.set_result([])
                                except Exception as e:
                                    logger.warning(f"设置失败Future结果失败: {e}")
                    logger.error(f"🚨 批处理失败 - 批次大小: {len(descriptions)}, 错误: {batch_error}")
                    import traceback
                    logger.error(f"🚨 批处理失败详细堆栈: {traceback.format_exc()}")

        finally:
            async with self.lock:
                self.processing = False
                # {{ AURA-X: Fix - 清理任务引用并检查是否还有待处理请求. Approval: 寸止(ID:1738232200). }}
                if hasattr(self, '_batch_task'):
                    delattr(self, '_batch_task')  # 清理任务引用

                if self.pending_requests:
                    # 如果还有待处理请求，启动新的批次处理
                    self._batch_task = asyncio.create_task(self._process_batch())




class FAISSVectorStore:
    """FAISS高性能向量存储 - 并发安全版本"""

    def __init__(self):
        self.examples = []
        self.metadata = []
        self.embeddings = []
        self.index = None
        self.dimension = None
        self.initialized = False
        # {{ AURA-X: Fix - 延迟初始化锁，避免事件循环问题. Approval: 寸止(ID:1738232300). }}
        self._search_lock = None

    def add_examples(self, examples: List[Dict], embeddings: List[List[float]], metadata: List[Dict]):
        """添加示例和向量"""
        self.examples.extend(examples)
        self.metadata.extend(metadata)
        self.embeddings.extend(embeddings)

        if not self.dimension and embeddings:
            self.dimension = len(embeddings[0])

        self._build_index()

    def _build_index(self):
        """构建FAISS索引"""
        if not self.embeddings or not FAISS_AVAILABLE:
            return

        try:
            # 转换为numpy数组
            embeddings_array = np.array(self.embeddings, dtype=np.float32)

            # 创建FAISS索引
            if self.dimension:
                # {{ AURA-X: Modify - 优化FAISS索引配置，提升多线程性能. Approval: 寸止(ID:1738230400). }}
                # 对于小数据集，直接使用简单索引
                if len(self.embeddings) < 5000:
                    self.index = faiss.IndexFlatIP(self.dimension)
                    # IndexFlatIP不需要设置nprobe，它是精确搜索
                else:
                    # 大数据集使用IVF索引，优化多线程性能
                    nlist = min(100, len(self.embeddings) // 40)  # 确保每个聚类有足够的点
                    quantizer = faiss.IndexFlatIP(self.dimension)
                    self.index = faiss.IndexIVFFlat(quantizer, self.dimension, nlist)
                    self.index.train(embeddings_array)
                    # 设置搜索参数
                    self.index.nprobe = min(10, nlist)  # 平衡精度和速度

                self.index.add(embeddings_array)
                self.initialized = True
                logger.info(f"✅ FAISS索引构建完成: {len(self.embeddings)}个向量, 维度{self.dimension}, 多线程已启用")

        except Exception as e:
            logger.error(f"FAISS索引构建失败: {e}")
            self.initialized = False

    async def search(self, query_embedding: List[float], top_k: int = 20) -> List[Tuple[int, float]]:
        """FAISS向量检索 - 并发安全版本"""
        if not self.initialized or not self.index:
            return []

        # {{ AURA-X: Fix - 延迟初始化锁，确保在正确的事件循环中创建. Approval: 寸止(ID:1738232300). }}
        if self._search_lock is None:
            self._search_lock = asyncio.Lock()

        # {{ AURA-X: Fix - 修复asyncio.wait_for使用方式，添加超时保护. Approval: 寸止(ID:1738232300). }}
        try:
            # 使用超时保护整个检索过程
            async def _do_search():
                async with self._search_lock:
                    query_array = np.array([query_embedding], dtype=np.float32)
                    scores, indices = self.index.search(query_array, min(top_k, len(self.embeddings)))

                    results = []
                    for score, idx in zip(scores[0], indices[0]):
                        if idx >= 0:  # FAISS返回-1表示无效结果
                            results.append((int(idx), float(score)))

                    return results

            return await asyncio.wait_for(_do_search(), timeout=180.0)

        except asyncio.TimeoutError:
            logger.error("🚨 FAISS检索超时，可能存在死锁")
            return []
        except Exception as e:
            logger.error(f"FAISS检索失败: {e}")
            return []


class ExampleRetriever:
    """高性能NER任务示例检索器 - 支持FAISS+批处理"""

    def __init__(self):
        self.config = CONFIG.get('retrieval_config', {})
        self.model_service = model_service

        # 选择向量存储后端
        if not FAISS_AVAILABLE:
            raise ImportError("FAISS is not available, which is required for the vector store. Please install it using 'pip install faiss-cpu' or 'pip install faiss-gpu'.")
        self.vector_store = FAISSVectorStore()

        # {{ AURA-X: Fix - 统一批处理参数，从CONFIG获取配置. Approval: 寸止(ID:1738232200). }}
        embedding_batch_size = min(CONFIG.get('batch_size', 64), 32)  # 嵌入API限制32个
        batch_timeout = CONFIG.get('batch_delay', 1.0) * 0.5  # 使用配置延迟的一半作为聚合时间
        self.batch_processor = BatchProcessor(batch_size=embedding_batch_size, timeout=batch_timeout)
        self.initialized = False
        
        logger.info("示例检索器初始化完成")




    async def _load_pkl_cache(self, pkl_file: str, data_path: str) -> bool:
        """加载pkl缓存"""
        try:
            with open(pkl_file, 'rb') as f:
                cached_data = pickle.load(f)

            # 验证缓存
            if not self._validate_cache(cached_data, data_path):
                logger.warning("缓存验证失败，重新生成...")
                return False

            examples = cached_data['examples']
            embeddings = cached_data['embeddings']
            metadata = cached_data.get('metadata', [{}] * len(examples))
            self.vector_store.add_examples(examples, embeddings, metadata)

            logger.info(f"从pkl缓存加载 {len(examples)} 个示例")
            self.initialized = True
            return True

        except Exception as e:
            logger.warning(f"加载pkl缓存失败: {e}")
            return False

    async def _migrate_json_to_pkl(self, json_file: str, pkl_file: str, data_path: str) -> bool:
        """将JSON缓存迁移到pkl格式"""
        try:
            with open(json_file, 'r', encoding='utf-8') as f:
                json_data = json.load(f)

            # 添加版本信息
            pkl_data = {
                'examples': json_data['examples'],
                'embeddings': json_data['embeddings'],
                'dataset_path': data_path,
                'created_at': json_data.get('created_at', time.time()),
                'version': '1.0',
                'dataset_hash': self._get_dataset_hash(data_path)
            }

            # 保存为pkl格式
            with open(pkl_file, 'wb') as f:
                pickle.dump(pkl_data, f)

            # 删除旧的JSON缓存
            os.remove(json_file)

            # 加载到向量存储
            metadata = pkl_data.get('metadata', [{}] * len(pkl_data['examples']))
            self.vector_store.add_examples(pkl_data['examples'], pkl_data['embeddings'], metadata)
            logger.info(f"成功迁移并加载 {len(pkl_data['examples'])} 个示例")
            self.initialized = True
            return True

        except Exception as e:
            logger.warning(f"迁移JSON缓存失败: {e}")
            return False

    def _validate_cache(self, cached_data: Dict[str, Any], data_path: str) -> bool:
        """验证缓存有效性"""
        try:
            # 检查必需字段
            required_fields = ['examples', 'embeddings', 'dataset_path']
            if not all(field in cached_data for field in required_fields):
                return False

            # 检查数据集是否已更改
            if cached_data['dataset_path'] != data_path:
                return False

            # 检查数据集文件哈希（如果存在）
            if 'dataset_hash' in cached_data:
                current_hash = self._get_dataset_hash(data_path)
                if cached_data['dataset_hash'] != current_hash:
                    logger.info("数据集文件已更改，缓存失效")
                    return False

            return True

        except Exception:
            return False

    def _get_dataset_hash(self, data_path: str) -> str:
        """获取数据集文件哈希"""
        try:
            with open(data_path, 'rb') as f:
                content = f.read()
            return hashlib.md5(content).hexdigest()
        except Exception:
            return ""

    async def _generate_embeddings_in_batches(self, texts: List[str], batch_size: int = 100, max_concurrent: int = 10) -> List[List[float]]:
        """🚀 并发批量生成嵌入向量，大幅提升效率"""
        total_batches = (len(texts) + batch_size - 1) // batch_size

        logger.info(f"🚀 开始并发生成嵌入向量: {len(texts)} 个文本, {total_batches} 个批次, 最大并发: {max_concurrent} (优化版)")

        # 创建批次任务
        batch_tasks = []
        for i in range(0, len(texts), batch_size):
            batch_texts = texts[i:i + batch_size]
            batch_num = i // batch_size + 1
            batch_tasks.append(self._process_single_batch(batch_texts, batch_num, total_batches))

        # 使用信号量控制并发数
        semaphore = asyncio.Semaphore(max_concurrent)

        async def process_with_semaphore(task):
            async with semaphore:
                return await task

        # 并发执行所有批次
        try:
            batch_results = await asyncio.gather(*[process_with_semaphore(task) for task in batch_tasks])

            # 合并结果
            all_embeddings = []
            for batch_embeddings in batch_results:
                if batch_embeddings:
                    all_embeddings.extend(batch_embeddings)
                else:
                    logger.error("某个批次嵌入生成失败")
                    return []

            logger.info(f"✅ 并发嵌入生成完成: {len(all_embeddings)} 个向量")
            return all_embeddings

        except Exception as e:
            logger.error(f"❌ 并发嵌入生成失败: {e}")
            return []

    async def _process_single_batch(self, batch_texts: List[str], batch_num: int, total_batches: int) -> List[List[float]]:
        """处理单个批次的嵌入生成"""
        try:
            logger.info(f"🔄 处理批次 {batch_num}/{total_batches}: {len(batch_texts)} 个文本")
            batch_embeddings = await self.model_service.get_embeddings_async(batch_texts)
            if batch_embeddings:
                logger.info(f"✅ 批次 {batch_num} 完成")
                return batch_embeddings
            else:
                logger.error(f"❌ 批次 {batch_num} 嵌入生成失败")
                return []
        except Exception as e:
            logger.error(f"❌ 批次 {batch_num} 处理失败: {e}")
            return []

    async def _generate_and_cache_vectors(self, data_path: str, pkl_cache_file: str) -> bool:
        """生成向量并保存到pkl缓存"""
        try:
            # 加载数据集
            with open(data_path, 'r', encoding='utf-8') as f:
                examples = json.load(f)

            # 批量生成语义向量（避免API超时）
            texts = [example.get('text', '') for example in examples]
            embeddings = await self._generate_embeddings_in_batches(texts, batch_size=50)

            if not embeddings or len(embeddings) != len(examples):
                logger.error(f"嵌入生成失败: 预期 {len(examples)}, 实际 {len(embeddings) if embeddings else 0}")
                return False

            # 添加到向量存储
            metadata = [{}] * len(examples)  # 简单metadata
            self.vector_store.add_examples(examples, embeddings, metadata)

            # 保存到pkl缓存
            cache_data = {
                'examples': examples,
                'embeddings': embeddings,
                'dataset_path': data_path,
                'created_at': time.time(),
                'version': '1.0',
                'dataset_hash': self._get_dataset_hash(data_path)
            }

            with open(pkl_cache_file, 'wb') as f:
                pickle.dump(cache_data, f)

            logger.info(f"向量存储初始化完成，生成并缓存 {len(examples)} 个示例")
            self.initialized = True
            return True

        except Exception as e:
            logger.error(f"生成向量缓存失败: {e}")
            return False

    async def initialize_vector_store(self, data_path: Optional[str] = None) -> bool:
        """🔍 KISS原则：检测有无向量库，有则继续，没有就生成"""
        try:
            # 获取数据集路径
            if data_path is None:
                data_path = get_current_dataset_path()

            if not os.path.exists(data_path):
                logger.warning(f"数据集文件不存在: {data_path}")
                return False

            # 简单的缓存检测
            cache_dir = CONFIG.get('vector_cache_dir', './cache/vector')
            os.makedirs(cache_dir, exist_ok=True)

            # 🔍 修复：包含数据集名称标识，避免不同数据集混淆
            from config import CONFIG as GLOBAL_CONFIG
            current_dataset_key = GLOBAL_CONFIG.get('current_dataset', 'unknown')
            file_name = os.path.basename(data_path).replace('.json', '')
            pkl_cache_file = os.path.join(cache_dir, f"{current_dataset_key}_{file_name}_vectors.pkl")

            # 检测向量库是否存在
            if os.path.exists(pkl_cache_file):
                logger.info("🔍 检测到现有向量库，正在加载...")
                if await self._load_pkl_cache(pkl_cache_file, data_path):
                    logger.info("✅ 向量库加载成功")
                    return True
                else:
                    logger.warning("⚠️ 向量库加载失败，重新生成...")

            # 向量库不存在或损坏，生成新的
            logger.info("🚀 向量库不存在，开始生成...")
            success = await self._generate_and_cache_vectors(data_path, pkl_cache_file)
            if success:
                logger.info("✅ 向量库生成完成")
            return success

        except Exception as e:
            logger.error(f"向量库初始化失败: {e}")
            return False




    async def _generate_query_embedding(self, query: str) -> List[float]:
        """生成查询嵌入 - 使用批处理器优化"""
        try:
            # 使用批处理器生成嵌入
            request_id = f"query_{hash(query)}_{time.time()}"
            embedding = await self.batch_processor.add_request(request_id, query)
            if not embedding:
                logger.warning(f"🚨 查询嵌入为空: query='{query[:50]}...', request_id={request_id}")
            return embedding if embedding else []
        except Exception as e:
            logger.error(f"🚨 生成查询嵌入失败: query='{query[:50]}...', 错误: {e}")
            import traceback
            logger.error(f"🚨 查询嵌入失败详细堆栈: {traceback.format_exc()}")
            return []

    def _cosine_similarity(self, vec1: List[float], vec2: List[float]) -> float:
        """计算余弦相似度"""
        try:
            vec1_array = np.array(vec1)
            vec2_array = np.array(vec2)
            
            # 计算余弦相似度
            dot_product = np.dot(vec1_array, vec2_array)
            norm1 = np.linalg.norm(vec1_array)
            norm2 = np.linalg.norm(vec2_array)
            
            if norm1 == 0 or norm2 == 0:
                return 0.0
                
            similarity = dot_product / (norm1 * norm2)
            return float(similarity)
        except Exception as e:
            logger.error(f"计算余弦相似度失败: {e}")
            return 0.0

    # {{ AURA-X: Remove - 删除废弃的重排器代码，简化系统架构. Approval: 寸止(ID:1738232000). }}
    # 重排器功能已废弃，当前系统直接使用FAISS检索结果


    async def simple_retrieve(self, description: str, k: int) -> List[Dict[str, Any]]:
        """
        高性能检索方法 - 使用FAISS+批处理优化

        Args:
            description: LLM生成的检索描述
            k: 需要的示例数量

        Returns:
            List[Dict]: k个示例
        """
        try:
            if not self.initialized:
                logger.warning("⚠️ 向量存储未初始化")
                return []

            logger.info(f"🔍 高性能检索: {description[:50]}..., k={k}")

            # 生成查询嵌入（使用批处理器）
            query_embedding = await self._generate_query_embedding(description)
            if not query_embedding:
                logger.warning("查询嵌入生成失败")
                return []

            # 使用FAISS进行高速检索
            if isinstance(self.vector_store, FAISSVectorStore) and self.vector_store.initialized:
                # {{ AURA-X: Fix - 调用异步FAISS检索方法. Approval: 寸止(ID:1738232000). }}
                search_results = await self.vector_store.search(query_embedding, top_k=k)

                results = []
                for idx, score in search_results:
                    if idx < len(self.vector_store.examples):
                        example = self.vector_store.examples[idx]
                        results.append({
                            'text': example.get('text', ''),
                            'label': example.get('label', {}),
                            'similarity_score': float(score)
                        })

                logger.info(f"✅ FAISS检索完成: 返回 {len(results)} 个示例")
                return results
            else:
                logger.warning("⚠️ FAISS向量存储未初始化")
                return []

        except Exception as e:
            logger.error(f"检索失败: {e}")
            return []




# 全局单例
example_retriever = ExampleRetriever()
