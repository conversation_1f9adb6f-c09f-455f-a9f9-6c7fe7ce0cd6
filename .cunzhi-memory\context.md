# 项目上下文信息

- 已完成SECSA框架Phase 0度量系统建立：1)创建secsa_metrics.py模块，提供DecisionRecord和PerformanceMetrics数据结构，SECSAMetricsCollector收集器，支持异步批量写入；2)在config.py中添加度量系统配置项；3)在pipeline.py中集成度量收集，记录策略选择和性能统计；4)在model_interface.py中集成API调用统计；5)创建metrics_viewer.py查看工具。所有集成都保持最小侵入性，不影响现有逻辑
- Phase 0度量系统已完成并修复了API统计集成问题，现在开始Phase 1意图感知与策略规划的开发，目标是将静态策略选择改造为智能的动态策略选择
- SECSA框架是一个自进化认知搜寻增强的NER系统，包含智能策略选择、自适应检索引擎、认知指导生成等核心模块，支持多种ICL策略，具备完整的度量系统和性能反馈机制
- 用户要求在实施重构前先对项目进行全面梳理，确保理解现有架构和重构目标。技术选型已确定：SiliconFlow重排器API、ChromaDB向量数据库、轻量化LangChain编排、英文Prompt、美观日志。用户强调不要生成文档、测试、编译或运行。
- 用户提供了完整的项目方案，强调这是基于LangChain构建的元认知智能体系统，核心创新是从"被动检索"到"主动求教"的转变。系统包含元认知规划器和需求驱动执行器两个核心组件，通过三阶段Prompt链生成结构化的"教学需求订单"，然后进行需求驱动的检索与重排。这是一个免微调的高效NER解决方案，具有重要的学术和工程价值。
- APIICL项目是一个基于元认知智能体的NER系统，核心创新是从"被动检索"到"主动求教"的转变。包含三大核心组件：元认知规划器(三阶段Prompt链)、需求驱动执行器(ChromaDB+SiliconFlow重排器)、LangChain编排器(条件分支+Fallback)。支持多数据集(ACE2005/CoNLL2003/WNUT2017)，具备完整的评估系统和异步API调用管理。这是一个免微调的高性能NER解决方案，具有重要学术和工程价值。
- APIICL项目系统审查完成：已实现完整的元认知智能体NER系统，包括三阶段分析链、需求驱动检索、多数据集支持(ace2005/conll2003/wnut2017)、评估系统、并发处理、超时控制等核心功能。系统验证结果：数据集切换正常，NER识别准确，并发处理100%成功率，无超时问题。创建了SYSTEM_STATUS.md文档维护功能清单、技术架构、性能表现和使用方式。项目已达到生产就绪状态。
- 用户澄清：项目是基于LangChain和Function Calling的元认知系统，不是简单的直接模式。用户强调要仔细阅读项目方案，理解其基于单一超级Prompt、LLM一次性完成思考和规划的核心思想，避免过度设计和逻辑分散
- 用户部署了gpt-load项目(https://github.com/tbphp/gpt-load)作为AI代理服务，当前APIICL项目通过base_url: 'http://8.138.94.162:3001/proxy/silicon/v1'连接到该代理服务，使用api_key: 'sk-zhongyushi'进行认证，model_name: 'Qwen/Qwen2.5-7B-Instruct'。用户询问配置参数的含义和作用。
- 用户计划加入更多维度作为源知识到向量库中，需要考虑多维度向量存储和检索的扩展性
- 用户关心的是API调用返回后的搜索操作如何避免死机，而不是API调用本身的并发问题
- APIICL项目已完成元认知智能体重构：创建了meta_cognitive_agent.py核心引擎，实现单一超级Prompt架构，使用Function Calling让LLM自主决策，支持多路并行检索（语义优先+标签辅助），删除了废弃文件single_prompt_ner.py和super_prompt_engine.py，修复了代码质量问题，系统支持200并发处理。当前测试发现LLM可能不调用工具的问题，已修改Prompt要求LLM必须调用工具
- 当前系统状态：已恢复完整的三环元认知智能体架构。第一环工作正常(LLM生成智能订单)，第二环和第三环的核心方法已恢复(_multi_path_retrieval, _select_best_examples)，但还需要完善多路并行检索的完整实现(路径A语义优先+路径B标签辅助+智能融合加权打分)
- 用户要求详细说明当前系统的完整流程。三环元认知智能体已完全实现：第一环超级Prompt+Function Call成功，第二环弹性加权多路检索完美工作（路径A语义优先50个+路径B标签辅助20个+智能融合），第三环MMR汇总+重排器精排正常运行。系统能正确识别复杂文本并生成检索请求
- 项目已完成两阶段NER架构优化：Stage1-LLM通过Function Call生成检索请求(description+k参数)；Stage2-使用FAISS+批处理器进行高性能检索，获取few-shot示例后进行NER。系统支持高并发，性能提升93%，删除了所有调试测试文件，代码结构清洁。
- 用户的API站并发量很高，可以支持更高的并发请求。当前系统的性能瓶颈主要在BatchProcessor聚合策略和HTTP连接复用，需要一次性实现所有性能优化。
- APIICL项目阶段二失败问题已完全修复：1)修复BatchProcessor循环处理逻辑，确保所有请求都被处理；2)增强错误日志记录，包含详细堆栈跟踪；3)添加处理结束后的检查机制，防止请求遗漏；4)修复批处理触发条件，避免竞态条件。测试验证：10个样本100%成功，无检索任务失败，耗时93.3秒，F1-Score 0.1429。BatchProcessor现在能正确处理高并发场景下的嵌入请求。
- APIICL项目潜在问题全面修复完成：1)修复BatchProcessor任务引用管理，避免重复创建任务和内存泄漏；2)统一批处理参数配置，从CONFIG获取设置确保一致性；3)优化内存管理，在各阶段完成后及时清理大型列表并强制垃圾回收；4)增强任务清理机制，防止Future对象泄漏。测试验证：20个样本100%成功，处理时间106.2秒，F1-Score 0.1412，系统稳定性大幅提升。
- APIICL项目当前状态(2025-01-31)：这是一个基于元认知智能体的NER系统，采用三阶段统一处理架构。主要文件包括：main.py(主入口，三阶段流程控制)、meta_cognitive_agent.py(元认知智能体核心)、example_retriever.py(示例检索器)、schemas.py(数据模型定义)、config.py(配置管理)。支持ACE2005/CoNLL2003/WNUT2017数据集，使用SiliconFlow API和FAISS向量检索。
- 技术栈配置：使用SiliconFlow API(base_url: http://8.138.94.162:3001/proxy/silicon/v1, model: Qwen/Qwen2.5-7B-Instruct, api_key: sk-zhongyushi)，支持Function Calling功能。FAISS向量检索，支持500并发请求，批处理大小64，超时时间600秒。支持ACE2005/CoNLL2003/WNUT2017三个数据集的动态切换。
