# 常用模式和最佳实践

- SiliconFlow支持Function Calling功能，可以让模型调用外部工具增强能力。支持Qwen/Qwen3-8B等多个模型，需要在请求中添加tools参数定义可调用的函数
- API支持Function Calling，可以让LLM直接调用NER工具，改进思路：让模型自主决定何时使用哪些工具，而不是预先定义复杂的策略选择逻辑
- 用户希望为Function Calling NER系统添加训练能力，让系统能够从经验中学习和改进，需要设计简单的训练过程
- 高性能检索器实现模式：使用FAISS向量数据库替代简单向量存储，实现BatchProcessor异步批处理器减少API调用，支持自动后端选择(FAISS可用时使用高性能后端，否则回退到SimpleVectorStore)，检索速度提升10-100倍。
- 当前实现模式：main.py实现三阶段统一处理(阶段1生成检索请求+阶段2统一检索+阶段3统一NER)，使用批量并发处理和缓存机制。meta_cognitive_agent.py实现两阶段NER流程(Stage1通过Function Call生成检索请求，Stage2基于few-shot示例进行NER)。使用RetrieveNERExamplesTool进行Function Calling，支持高并发和性能优化。
